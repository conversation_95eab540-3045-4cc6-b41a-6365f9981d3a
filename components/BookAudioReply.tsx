'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { ReactionSystem } from './ReactionSystem'
import { MessageCircle, User } from 'lucide-react'
import { Day1Badge } from './Day1Badge'

interface BookAudioReply {
  id: string
  user_id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  parent_reply_id?: string | null
  created_at: string
  reaction_counts?: Record<string, number>
  userReaction?: string | null
  user?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
}

interface BookAudioReplyProps {
  reply: BookAudioReply
  bookId: string
  currentUserId?: string
  onReply?: (parentReplyId: string) => void
  onNestedReply?: (parentReplyId: string, audioBlob: Blob, duration: number) => Promise<void>
  level?: number // For nested replies
  maxNestingLevel?: number
}

export function BookAudioReply({
  reply,
  bookId,
  currentUserId,
  onReply,
  onNestedReply,
  level = 0,
  maxNestingLevel = 3
}: BookAudioReplyProps) {
  const [reactions, setReactions] = useState(reply.reaction_counts || {})
  const [userReaction, setUserReaction] = useState(reply.userReaction)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    return `${Math.floor(diffInSeconds / 86400)}d ago`
  }

  const handleReply = () => {
    if (level < maxNestingLevel) {
      setShowReplyRecorder(true)
    } else {
      onReply?.(reply.id)
    }
  }

  const handleReactionUpdate = (newReactions: Record<string, number>, newUserReaction: string | null) => {
    setReactions(newReactions)
    setUserReaction(newUserReaction)
  }

  const handleAudioReply = async (audioBlob: Blob, duration: number) => {
    try {
      await onNestedReply?.(reply.id, audioBlob, duration)
      setShowReplyRecorder(false)
    } catch (error) {
      console.error('Error creating nested reply:', error)
      alert('Failed to create reply. Please try again.')
    }
  }

  // Calculate threading shade based on level - more subtle and elegant
  const getThreadingShade = (level: number) => {
    const shades = [
      'border-l-gray-200 bg-white',
      'border-l-blue-200 bg-blue-50/20',
      'border-l-purple-200 bg-purple-50/20',
      'border-l-indigo-200 bg-indigo-50/20'
    ]
    return shades[Math.min(level, shades.length - 1)]
  }

  // Add engagement indicators for deep threading
  const getEngagementIndicator = (level: number) => {
    if (level >= 3) return '🔥'
    if (level >= 2) return '💬'
    return ''
  }

  // Get threading padding based on level - cleaner spacing
  const getThreadingPadding = (level: number) => {
    if (level === 0) return ''
    const borderWidth = level === 1 ? 'border-l-3' : level === 2 ? 'border-l-4' : 'border-l-4'
    const marginLeft = level === 1 ? 'ml-4' : level === 2 ? 'ml-6' : 'ml-8'
    return `${borderWidth} ${getThreadingShade(level)} pl-3 sm:pl-4 ${marginLeft} relative`
  }

  return (
    <div className={getThreadingPadding(level)}>
      {/* Visual nesting indicator - more subtle */}
      {level > 0 && (
        <div className={`absolute -left-1.5 top-3 w-3 h-3 rounded-full border border-white shadow-sm z-10 ${
          level === 1 ? 'bg-blue-400' :
          level === 2 ? 'bg-purple-400' :
          'bg-indigo-400'
        }`}></div>
      )}

      <div className="bg-white rounded-lg p-3 sm:p-4 mb-3 shadow-sm border border-gray-100">
        {/* Clean reply indicator */}
        {level > 0 && (
          <div className="flex items-center gap-2 mb-2 text-xs">
            <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full font-medium text-xs">
              ↳ Reply {getEngagementIndicator(level)}
            </span>
          </div>
        )}

        {/* User Info */}
        <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3">
          {reply.user?.profile_picture_url || reply.user?.avatar ? (
            <div>
              <Image
                src={reply.user.profile_picture_url || reply.user.avatar || ''}
                alt={reply.user?.name || 'Anonymous'}
                width={32}
                height={32}
                className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover"
              />
            </div>
          ) : (
            <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-gray-400 flex items-center justify-center">
              <User className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            </div>
          )}

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 sm:gap-2">
              <p className="font-medium text-xs sm:text-sm text-gray-900 truncate">
                {reply.user?.name || 'Anonymous'}
              </p>
              {reply.user?.has_day1_badge && (
                <Day1Badge
                  signupNumber={reply.user.signup_number}
                  size="sm"
                  showTooltip={false}
                />
              )}
              {level > 0 && getEngagementIndicator(level) && (
                <span className="text-xs">{getEngagementIndicator(level)}</span>
              )}
            </div>
            <p className="text-xs text-gray-500">{formatTimeAgo(reply.created_at)}</p>
          </div>
        </div>

        {/* Audio Player */}
        <div className="mb-3">
          <AudioPlayer
            audioUrl={reply.audio_url}
            duration={reply.duration_seconds}
            className="w-full"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Reaction System */}
          <ReactionSystem
            contentType="book_audio_reply"
            contentId={reply.id}
            bookId={bookId}
            currentUserId={currentUserId}
            initialReactions={reactions}
            userReaction={userReaction}
            onReactionUpdate={handleReactionUpdate}
          />

          {level < maxNestingLevel && (
            <button
              onClick={handleReply}
              disabled={!currentUserId}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all text-blue-600 hover:bg-blue-50 min-h-[44px] ${
                !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="text-xs sm:text-sm font-medium">Reply</span>
            </button>
          )}
        </div>

        {/* Nested Reply Recorder */}
        {showReplyRecorder && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="mb-2">
              <p className="text-sm font-medium text-gray-700">Reply to {reply.user?.name}</p>
            </div>
            <AudioRecorder
              onRecordingComplete={handleAudioReply}
              onCancel={() => setShowReplyRecorder(false)}
              maxDuration={9}
              className="w-full"
            />
          </div>
        )}
      </div>
    </div>
  )
}
